/*
* ===============================================================
*                    SMART DIGITAL ID CARD GPS TRACKER
* ===============================================================
* 
* FEATURES:
* - Real-time GPS coordinate display
* - Campus geofencing for student safety
* - WiFi connectivity for data transmission
* - Offline data storage with configurable buffer limits
* - Automatic reconnection capabilities
* - Timezone offset configuration
* - Multiple debug levels for troubleshooting
* - Status monitoring with detailed error codes
* 
* WORKING:
* 1. Initializes GPS module on Serial1 with custom RX/TX pins
* 2. Connects to WiFi network using provided credentials
* 3. Continuously reads and displays GPS coordinates
* 4. Processes and validates GPS coordinates
* 5. Checks if student is within campus boundary
* 6. Transmits location data to monitoring system
* 7. Provides real-time status feedback via serial monitor
* 
* HARDWARE REQUIREMENTS:
* - ESP32 development board
* - GPS module (NMEA compatible - NEO-6M/NEO-8M)
* - WiFi network access
* 
* PIN CONNECTIONS:
* - GPS RX: GPIO16 (ESP32 TX1)
* - GPS TX: GPIO17 (ESP32 RX1)
* - GPS VCC: 3.3V
* - GPS GND: GND
* 
* Author: Smart Campus Project
* Version: 2.0
* Last Modified: Current Date
* 
* ===============================================================
*/

#include <GeoLinker.h>

// ==================================================================
//                    HARDWARE CONFIGURATION
// ==================================================================
// GPS Serial Communication Setup
HardwareSerial gpsSerial(1);  // Using Serial1 for GPS communication
#define GPS_RX 16             // GPIO16 connected to GPS module TX pin
#define GPS_TX 17             // GPIO17 connected to GPS module RX pin

// GPS Communication Settings
#define GPS_BAUD 9600         // Standard NMEA GPS baud rate (9600 bps)

// ==================================================================
//                    NETWORK CONFIGURATION
// ==================================================================
// WiFi Network Credentials
const char* ssid = "SRINIVAS";       // Your WiFi network name (SSID)
const char* password = "9298709463";   // Your WiFi network password

// ==================================================================
//                   GEOLINKER CONFIGURATION
// ==================================================================
// API Authentication
const char* apiKey = "mYiDN5sMG51F";    // Your unique GeoLinker API key
const char* deviceID = "ESP-32_Tracker"; // Unique identifier for this device

// Data Transmission Settings
const uint16_t updateInterval = 5;       // How often to send data (seconds)
const bool enableOfflineStorage = true; // Store data when offline
const uint8_t offlineBufferLimit = 20;  // Maximum offline records to store

// Connection Management
const bool enableAutoReconnect = true;  // Automatically reconnect to WiFi

// Timezone Configuration
const int8_t timeOffsetHours = 5;       // Timezone hours offset from UTC
const int8_t timeOffsetMinutes = 30;    // Timezone minutes offset from UTC
                                      // Example: IST = UTC+5:30

// Create GeoLinker instance
GeoLinker geo;

// ==================================================================
//                    CAMPUS BOUNDARY CONFIGURATION
// ==================================================================
// Define your campus boundary (replace with actual coordinates)
struct Point {
  double lat;
  double lng;
};

// Campus boundary points (walk around your campus to get these coordinates)
const Point CAMPUS_BOUNDARY[] = {
  {12.9716, 77.5946},  // Point 1 - Main Gate
  {12.9720, 77.5950},  // Point 2 - Library Corner
  {12.9725, 77.5948},  // Point 3 - Sports Complex
  {12.9722, 77.5940},  // Point 4 - Hostel Area
  {12.9718, 77.5938},  // Point 5 - Cafeteria
  {12.9714, 77.5942},  // Point 6 - Admin Block
  {12.9716, 77.5946}   // Point 7 - Back to Main Gate (close polygon)
};

const int BOUNDARY_POINTS = 7;

// ==================================================================
//                    CAMPUS GEOFENCING FUNCTIONS
// ==================================================================
bool isInsideCampus(double lat, double lng) {
  // Point-in-polygon algorithm to check if student is inside campus
  int i, j;
  bool inside = false;
  
  for (i = 0, j = BOUNDARY_POINTS - 1; i < BOUNDARY_POINTS; j = i++) {
    if (((CAMPUS_BOUNDARY[i].lng > lng) != (CAMPUS_BOUNDARY[j].lng > lng)) &&
        (lat < (CAMPUS_BOUNDARY[j].lat - CAMPUS_BOUNDARY[i].lat) * 
         (lng - CAMPUS_BOUNDARY[i].lng) / 
         (CAMPUS_BOUNDARY[j].lng - CAMPUS_BOUNDARY[i].lng) + CAMPUS_BOUNDARY[i].lat)) {
      inside = !inside;
    }
  }
  
  return inside;
}

void triggerCampusAlert(double lat, double lng) {
  Serial.println("\n🚨🚨🚨 CAMPUS SECURITY ALERT 🚨🚨🚨");
  Serial.println("STUDENT HAS LEFT CAMPUS BOUNDARY!");
  Serial.print("Exit Location - Lat: ");
  Serial.print(lat, 6);
  Serial.print(", Lng: ");
  Serial.println(lng, 6);
  Serial.print("Google Maps: https://maps.google.com/?q=");
  Serial.print(lat, 6);
  Serial.print(",");
  Serial.println(lng, 6);
  Serial.println("🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨");
}

// ==================================================================
//                    INITIALIZATION SETUP
// ==================================================================
void setup() {
  // Initialize serial communication for debugging
  Serial.begin(115200);
  delay(1000);  // Allow serial to initialize
  
  Serial.println("\n" + String('=', 60));
  Serial.println("        SMART DIGITAL ID CARD GPS TRACKER");
  Serial.println("              Starting System...");
  Serial.println(String('=', 60));
  
  // Initialize GPS serial communication with custom pins
  gpsSerial.begin(GPS_BAUD, SERIAL_8N1, GPS_RX, GPS_TX);
  Serial.println("✓ GPS Serial initialized on pins 16(RX) and 17(TX)");
  
  // Test GPS module communication
  Serial.println("🔍 Testing GPS module communication...");
  gpsSerial.println("$PMTK314,0,1,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0*28");
  delay(1000);
  
  if (gpsSerial.available()) {
    Serial.println("✓ GPS module responding!");
  } else {
    Serial.println("⚠ No GPS response - Check wiring!");
  }
  
  // ==================================================================
  //                   GEOLINKER LIBRARY SETUP
  // ==================================================================
  // Initialize GeoLinker with GPS serial interface
  geo.begin(gpsSerial);
  Serial.println("✓ GeoLinker library initialized");
  
  // Configure API authentication
  geo.setApiKey(apiKey);
  Serial.println("✓ API key configured");
  
  // Set unique device identifier
  geo.setDeviceID(deviceID);
  Serial.println("✓ Device ID set: " + String(deviceID));
  
  // Configure data transmission interval
  geo.setUpdateInterval_seconds(updateInterval);
  Serial.println("✓ Update interval set to " + String(updateInterval) + " seconds");
  
  // Set debug verbosity level
  geo.setDebugLevel(DEBUG_BASIC);
  Serial.println("✓ Debug level set to BASIC");
  
  // Enable offline data storage capability
  geo.enableOfflineStorage(enableOfflineStorage);
  if(enableOfflineStorage) {
    Serial.println("✓ Offline storage enabled");
  }
  
  // Enable automatic WiFi reconnection
  geo.enableAutoReconnect(enableAutoReconnect);
  if(enableAutoReconnect) {
    Serial.println("✓ Auto-reconnect enabled");
  }
  
  // Set maximum offline buffer size
  geo.setOfflineBufferLimit(offlineBufferLimit);
  Serial.println("✓ Offline buffer limit set to " + String(offlineBufferLimit) + " records");
  
  // Configure timezone offset for accurate timestamps
  geo.setTimeOffset(timeOffsetHours, timeOffsetMinutes);
  Serial.println("✓ Timezone offset set to UTC+" + String(timeOffsetHours) + ":" + String(timeOffsetMinutes));
  
  // ==================================================================
  //                    NETWORK CONNECTION SETUP
  // ==================================================================
  // Configure for WiFi mode
  geo.setNetworkMode(GEOLINKER_WIFI);
  Serial.println("✓ Network mode set to WiFi");
  
  // Set WiFi network credentials
  geo.setWiFiCredentials(ssid, password);
  Serial.println("✓ WiFi credentials configured");
  
  // Attempt WiFi connection
  Serial.print("🌐 Connecting to WiFi network: ");
  Serial.println(ssid);
  if (!geo.connectToWiFi()) {
    Serial.println("⚠ WiFi connection failed!");
    Serial.println("📱 Device will continue with offline storage mode");
  } else {
    Serial.println("✓ WiFi connected successfully!");
  }
  
  Serial.println("\n" + String('=', 60));
  Serial.println("🎯 SMART ID CARD SETUP COMPLETE!");
  Serial.println("📍 Starting real-time location tracking...");
  Serial.println("🏫 Campus boundary monitoring active");
  Serial.println(String('=', 60) + "\n");
  
  delay(2000);
}

// ==================================================================
//                   MAIN PROGRAM LOOP
// ==================================================================
void loop() {
  // ==========================================
  //         GEOLINKER MAIN OPERATION
  // ==========================================
  // Execute main GeoLinker processing cycle
  uint8_t status = geo.loop();

  // ==========================================
  //    REAL-TIME COORDINATE DISPLAY
  // ==========================================

  Serial.println("\n" + String('=', 70));
  Serial.println("                 📍 STUDENT LOCATION TRACKER 📍");
  Serial.println(String('=', 70));

  // Check if GPS has a valid fix
  if (geo.isGPSFixed()) {
    // Get current coordinates
    double latitude = geo.getLatitude();
    double longitude = geo.getLongitude();
    int satellites = geo.getSatellites();

    // Display real-time location with enhanced formatting
    Serial.println("🌍 CURRENT REAL-TIME LOCATION:");
    Serial.println("┌─────────────────────────────────────────────────────────────┐");
    Serial.print("│ 📍 Latitude:   ");
    Serial.print(latitude, 6);  // 6 decimal places for precision
    Serial.println("                              │");
    Serial.print("│ 📍 Longitude:  ");
    Serial.print(longitude, 6);
    Serial.println("                              │");
    Serial.print("│ 🛰️  Satellites: ");
    Serial.print(satellites);
    Serial.println("                                        │");
    Serial.println("└─────────────────────────────────────────────────────────────┘");

    // Display location quality assessment
    Serial.print("📊 GPS SIGNAL QUALITY: ");
    if (satellites >= 6) {
      Serial.println("🟢 EXCELLENT (High Accuracy)");
    } else if (satellites >= 4) {
      Serial.println("🟡 GOOD (Moderate Accuracy)");
    } else if (satellites >= 3) {
      Serial.println("🟠 FAIR (Basic Accuracy)");
    } else {
      Serial.println("🔴 POOR (Low Accuracy)");
    }

    // Display Google Maps link for verification
    Serial.println("\n🗺️  LOCATION VERIFICATION:");
    Serial.print("   Google Maps: https://maps.google.com/?q=");
    Serial.print(latitude, 6);
    Serial.print(",");
    Serial.println(longitude, 6);

    // ==========================================
    //         CAMPUS BOUNDARY CHECKING
    // ==========================================
    Serial.println("\n🏫 CAMPUS BOUNDARY STATUS:");
    bool studentOnCampus = isInsideCampus(latitude, longitude);

    if (studentOnCampus) {
      Serial.println("   Status: ✅ STUDENT IS ON CAMPUS");
      Serial.println("   Safety: 🟢 SECURE - Within authorized area");
    } else {
      Serial.println("   Status: ❌ STUDENT HAS LEFT CAMPUS");
      Serial.println("   Safety: 🔴 ALERT - Outside authorized area");

      // Trigger campus security alert
      triggerCampusAlert(latitude, longitude);
    }

  } else {
    // GPS not fixed yet - show search status
    Serial.println("🔍 SEARCHING FOR GPS SIGNAL...");
    Serial.println("┌─────────────────────────────────────────────────────────────┐");
    Serial.println("│ Status: 🔄 Acquiring satellite signal...                    │");
    Serial.println("│ Action: 📡 Please ensure clear sky view                     │");
    Serial.print("│ Satellites visible: ");
    Serial.print(geo.getSatellites());
    Serial.println("                                      │");
    Serial.println("│ Tip: 🌤️  Move to outdoor location for better signal        │");
    Serial.println("└─────────────────────────────────────────────────────────────┘");
  }

  // ==========================================
  //         GEOLINKER STATUS DISPLAY
  // ==========================================
  if (status > 0) {
    Serial.println("\n📡 GEOLINKER CLOUD STATUS:");
    Serial.print("   Operation Result: ");

    // Interpret status codes and provide user feedback
    switch(status) {
      case STATUS_SENT:
        Serial.println("✅ Data transmitted successfully to cloud!");
        Serial.println("   📤 Location data uploaded to monitoring system");
        break;
      case STATUS_GPS_ERROR:
        Serial.println("❌ GPS module connection error!");
        Serial.println("   🔧 Check wiring: GPS TX→GPIO16, GPS RX→GPIO17");
        break;
      case STATUS_NETWORK_ERROR:
        Serial.println("⚠️  Network connectivity issue");
        Serial.println("   💾 Data buffered offline for later transmission");
        break;
      case STATUS_BAD_REQUEST_ERROR:
        Serial.println("❌ Server rejected request");
        Serial.println("   🔑 Check API key and data format");
        break;
      case STATUS_PARSE_ERROR:
        Serial.println("❌ GPS data parsing error");
        Serial.println("   📡 Invalid NMEA format received");
        break;
      case STATUS_INTERNAL_SERVER_ERROR:
        Serial.println("❌ GeoLinker server internal error");
        Serial.println("   ⏰ Try again later");
        break;
      default:
        Serial.print("❓ Unknown status code: ");
        Serial.println(status);
        break;
    }
  }

  // Display system timestamp
  Serial.println("\n⏰ System Time: " + String(millis()/1000) + " seconds since startup");
  Serial.println(String('=', 70) + "\n");

  // Update interval - adjust as needed for your application
  delay(3000);  // Update every 3 seconds for real-time monitoring
}
