/*
* ===============================================================
*                SMART GPS TRACKER - STANDARD LIBRARIES VERSION
* ===============================================================
* Uses only standard Arduino libraries - no custom GeoLinker needed
*/

#include <WiFi.h>
#include <TinyGPS++.h>
#include <HardwareSerial.h>

// ==================================================================
//                    HARDWARE CONFIGURATION
// ==================================================================
HardwareSerial gpsSerial(1);
#define GPS_RX 16
#define GPS_TX 17
#define GPS_BAUD 9600

// ==================================================================
//                    NETWORK CONFIGURATION
// ==================================================================
const char* ssid = "SRINIVAS";
const char* password = "9298709463";

// ==================================================================
//                    GPS AND CAMPUS CONFIGURATION
// ==================================================================
TinyGPSPlus gps;

// Your campus coordinates
const double CAMPUS_CENTER_LAT = 17.485194;  // 17°29'06.7"N
const double CAMPUS_CENTER_LNG = 78.608917;  // 78°36'32.1"E
const double CAMPUS_RADIUS = 0.005;          // ~500 meters

// ==================================================================
//                    CAMPUS FUNCTIONS
// ==================================================================
bool isInsideCampus(double lat, double lng) {
  double latDiff = lat - CAMPUS_CENTER_LAT;
  double lngDiff = lng - CAMPUS_CENTER_LNG;
  double distance = sqrt(latDiff * latDiff + lngDiff * lngDiff);
  return distance <= CAMPUS_RADIUS;
}

double getDistanceFromCampus(double lat, double lng) {
  double latDiff = lat - CAMPUS_CENTER_LAT;
  double lngDiff = lng - CAMPUS_CENTER_LNG;
  double latMeters = latDiff * 111000;
  double lngMeters = lngDiff * 111000 * cos(lat * PI / 180);
  return sqrt(latMeters * latMeters + lngMeters * lngMeters);
}

void triggerCampusAlert(double lat, double lng) {
  Serial.println("\n🚨🚨🚨 CAMPUS SECURITY ALERT 🚨🚨🚨");
  Serial.println("STUDENT HAS LEFT CAMPUS BOUNDARY!");
  Serial.print("Location: ");
  Serial.print(lat, 6);
  Serial.print(", ");
  Serial.println(lng, 6);
  Serial.print("Distance: ");
  Serial.print(getDistanceFromCampus(lat, lng), 0);
  Serial.println(" meters from campus");
  Serial.println("🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨");
}

// ==================================================================
//                    SETUP
// ==================================================================
void setup() {
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("\n" + String('=', 60));
  Serial.println("        SMART GPS TRACKER - STANDARD VERSION");
  Serial.println(String('=', 60));
  
  // Initialize GPS
  gpsSerial.begin(GPS_BAUD, SERIAL_8N1, GPS_RX, GPS_TX);
  Serial.println("✓ GPS initialized on pins 16(RX) and 17(TX)");
  
  // Test GPS communication
  Serial.println("🔍 Testing GPS module...");
  delay(1000);
  
  // Connect to WiFi
  WiFi.begin(ssid, password);
  Serial.print("🌐 Connecting to WiFi: ");
  Serial.println(ssid);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\n✓ WiFi connected!");
    Serial.print("IP: ");
    Serial.println(WiFi.localIP());
  } else {
    Serial.println("\n⚠ WiFi failed - continuing offline");
  }
  
  Serial.println("\n🎯 GPS Tracker Ready!");
  Serial.println("📍 Go outside for GPS signal...");
  Serial.println(String('=', 60) + "\n");
}

// ==================================================================
//                    MAIN LOOP
// ==================================================================
void loop() {
  // Read GPS data
  while (gpsSerial.available() > 0) {
    if (gps.encode(gpsSerial.read())) {
      if (gps.location.isValid()) {
        displayLocation();
      }
    }
  }
  
  // Show GPS search status if no fix
  if (millis() > 5000 && gps.charsProcessed() < 10) {
    Serial.println("🔍 No GPS data - check wiring!");
    delay(5000);
  } else if (!gps.location.isValid()) {
    Serial.println("🔍 Searching for GPS signal...");
    Serial.print("Satellites: ");
    Serial.println(gps.satellites.value());
    delay(2000);
  }
}

void displayLocation() {
  double lat = gps.location.lat();
  double lng = gps.location.lng();
  int satellites = gps.satellites.value();
  
  Serial.println("\n" + String('=', 70));
  Serial.println("                 📍 STUDENT LOCATION TRACKER 📍");
  Serial.println(String('=', 70));
  
  // Display coordinates
  Serial.println("🌍 CURRENT REAL-TIME LOCATION:");
  Serial.println("┌─────────────────────────────────────────────────────────────┐");
  Serial.print("│ 📍 Latitude:   ");
  Serial.print(lat, 6);
  Serial.println("                              │");
  Serial.print("│ 📍 Longitude:  ");
  Serial.print(lng, 6);
  Serial.println("                              │");
  Serial.print("│ 🛰️  Satellites: ");
  Serial.print(satellites);
  Serial.println("                                        │");
  Serial.println("└─────────────────────────────────────────────────────────────┘");
  
  // GPS quality
  Serial.print("📊 GPS QUALITY: ");
  if (satellites >= 6) {
    Serial.println("🟢 EXCELLENT");
  } else if (satellites >= 4) {
    Serial.println("🟡 GOOD");
  } else {
    Serial.println("🟠 FAIR");
  }
  
  // Google Maps link
  Serial.print("🗺️  Google Maps: https://maps.google.com/?q=");
  Serial.print(lat, 6);
  Serial.print(",");
  Serial.println(lng, 6);
  
  // Campus boundary check
  Serial.println("\n🏫 CAMPUS BOUNDARY STATUS:");
  bool onCampus = isInsideCampus(lat, lng);
  
  if (onCampus) {
    Serial.println("   Status: ✅ STUDENT IS ON CAMPUS");
    Serial.println("   Safety: 🟢 SECURE");
  } else {
    Serial.println("   Status: ❌ STUDENT LEFT CAMPUS");
    Serial.println("   Safety: 🔴 ALERT");
    triggerCampusAlert(lat, lng);
  }
  
  Serial.print("   Distance from center: ");
  Serial.print(getDistanceFromCampus(lat, lng), 0);
  Serial.println(" meters");
  
  Serial.println(String('=', 70) + "\n");
  delay(3000);
}
